import assert = require('assert');
import * as ee from '../../libraries/ee/index';
import {
    AudioManager,
    CrashlyticManager,
    crashlytics,
    DailyRewardManager,
    RewardInfo,
    RewardUtils,
    SceneName,
    SoundType,
    TrackAdsRewardWatch,
    TrackingManager,
} from '../../manager/gm_manager';
import {DailyRewardItem} from './DailyRewardItem';

const { ccclass, disallowMultiple, property, executeInEditMode } = cc._decorator;

export enum DailyRewardState {
    Received,
    Claim,
    NotClaim,
}

export abstract class DailyRewardCell extends cc.Component {
    /** Gets or sets this item's day index. */
    public abstract dayIndex: number;

    /** Gets or sets this item's reward state. */
    public abstract state: DailyRewardState;

    /** Gets or sets this item's rewards. */
    public abstract rewards: RewardInfo[];
}

/** Used in editor. */
@ccclass
@disallowMultiple
@executeInEditMode
export class DailyRewardCellImpl extends DailyRewardCell {
    @property({ type: cc.Prefab, visible: true })
    private readonly _itemPrefab: cc.Prefab | null = null;

    /** Displays rewardable items. */
    @property({ type: cc.Node, visible: true })
    private readonly _container: cc.Node | null = null;

    /** Displayed when this item is ready to claim. */
    @property({ type: cc.Node, visible: true })
    private readonly _claimButton: cc.Node | null = null;

    /** Displayed when this item is claimed. */
    @property({ type: cc.Node, visible: true })
    private readonly _claimedLayer: cc.Node | null = null;

    @property({ type: ee.LanguageComponent, visible: true })
    private readonly _dayLabel: ee.LanguageComponent | null = null;

    @property({ type: cc.Node, visible: true })
    private readonly _effectLayer: cc.Node | null = null;

    @property({ type: cc.Node, visible: true })
    private readonly _notClaimBackground: cc.Node | null = null;

    @property({ type: cc.Node, visible: true })
    private readonly _claimBackground: cc.Node | null = null;

    private get itemPrefab(): cc.Prefab {
        if (this._itemPrefab === null) {
            throw Error('Item not registered.');
        }
        return this._itemPrefab;
    }

    private get container(): cc.Node {
        if (this._container === null) {
            throw Error('Item not registered.');
        }
        return this._container;
    }

    private get claimButton(): cc.Node {
        if (this._claimButton === null) {
            throw Error('Item not registered.');
        }
        return this._claimButton;
    }

    private get claimedLayer(): cc.Node {
        if (this._claimedLayer === null) {
            throw Error('Item not registered.');
        }
        return this._claimedLayer;
    }

    private get dayLabel(): ee.LanguageComponent {
        if (this._dayLabel === null) {
            throw Error('Item not registered.');
        }
        return this._dayLabel;
    }

    private get effectLayer(): cc.Node {
        if (this._effectLayer === null) {
            throw Error('Item not registered.');
        }
        return this._effectLayer;
    }

    private get notClaimBackground(): cc.Node {
        if (this._notClaimBackground === null) {
            throw Error('Item not registered.');
        }
        return this._notClaimBackground;
    }

    private get claimBackground(): cc.Node {
        if (this._claimBackground === null) {
            throw Error('Item not registered.');
        }
        return this._claimBackground;
    }

    private _dayIndex = 0;
    private _state = DailyRewardState.Claim;
    private _rewards: RewardInfo[] = [];

    public get dayIndex(): number {
        return this._dayIndex;
    }

    public set dayIndex(value: number) {
        if (this._dayIndex !== value) {
            this._dayIndex = value;
            this.updateIndex();
        }
    }

    public get state(): DailyRewardState {
        return this._state;
    }

    public set state(value: DailyRewardState) {
        if (this._state !== value) {
            this._state = value;
            this.updateState();
        }
    }

    public get rewards(): RewardInfo[] {
        return this._rewards;
    }

    public set rewards(value: RewardInfo[]) {
        this._rewards = value;
        this.updateRewards();
    }

    private items: DailyRewardItem[] = [];

    protected onLoad(): void {
        assert(this._itemPrefab !== null);
        assert(this._container !== null);
        assert(this._claimButton !== null);
        assert(this._claimedLayer !== null);
        assert(this._dayLabel !== null);
        assert(this._effectLayer !== null);
        this.updateIndex();
        this.updateState();
        //this.updateRewards();
    }

    /** Registered in editor. */
    @crashlytics
    public onClaim(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onClaim, `${this.uuid}`);

        // FIXME: need to update reward items.
        this.state = DailyRewardState.Received;

        ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.ButtonPress);
        const dailyRewardManager = ee.ServiceLocator.resolve(DailyRewardManager);
        // RewardUtils.showRewardDialog(this._rewards, ee.ServiceLocator.resolve(ee.DialogManager),  SceneName.SceneDailyReward).then(
        //     (dialog) => {
        //             dialog
        //                 .setTitleKey("text_daily_reward")
        //                 .activeCloseButton(true)
        //                 .setAdsReward(TrackAdsRewardWatch.ClaimDailyReward)
        //
        //     }
        // );
        // Directly claim reward, not use reward dialog
        RewardUtils.claimReward(this._rewards, true);
        dailyRewardManager.claimReward();

        const day = dailyRewardManager.getCurrentRewardIndex();
        const trackingManager = ee.ServiceLocator.resolve(TrackingManager);
        trackingManager.trackConversionDailyGift(day + 1);
    }

    @crashlytics
    public onClaimWithAds(): void {
        ee.ServiceLocator.resolve(CrashlyticManager).logFunction(this, this.onClaim, `${this.uuid}`);

        // FIXME: need to update reward items.
        this.state = DailyRewardState.Received;

        ee.ServiceLocator.resolve(AudioManager).playSound(SoundType.ButtonPress);
        const dailyRewardManager = ee.ServiceLocator.resolve(DailyRewardManager);
        // Directly claim reward, not use reward dialog
        RewardUtils.claimReward([...this._rewards,...this._rewards], true);
        dailyRewardManager.claimReward();

        const day = dailyRewardManager.getCurrentRewardIndex();
        const trackingManager = ee.ServiceLocator.resolve(TrackingManager);
        trackingManager.trackConversionDailyGift(day + 1);
    }

    private updateIndex(): void {
        this.dayLabel.paramValues = [`${this.dayIndex + 1}`];
    }

    private updateState(): void {
        switch (this.state) {
            case DailyRewardState.Received:
                // this.claimButton.active = false;
                this.claimedLayer.active = true;
                // this.effectLayer.active = false;
                this.claimBackground.active = false;
                this.notClaimBackground.active = true;
                break;
            case DailyRewardState.Claim:
                // this.claimButton.active = true;
                this.claimedLayer.active = false;
                // this.effectLayer.active = true;
                // this.effectLayer.runAction(cc.repeatForever(cc.rotateBy(3, 360)));
                this.claimBackground.active = true;
                this.notClaimBackground.active = false;
                break;
            case DailyRewardState.NotClaim:
                // this.claimButton.active = false;
                this.claimedLayer.active = false;
                // this.effectLayer.active = false;
                this.claimBackground.active = false;
                this.notClaimBackground.active = true;
                break;
        }
        this.effectLayer.active = false;

    }

    private updateRewards(): void {
        for (let i = this.items.length, n = this.rewards.length; i < n; ++i) {
            const node = cc.instantiate(this.itemPrefab);
            const item = node.getComponent(DailyRewardItem);
            this.items.push(item);
            this.container.addChild(node);
        }
        for (let i = this.rewards.length, n = this.items.length; i < n; ++i) {
            this.items[i].node.active = false;
        }
        for (let i = 0, n = this.rewards.length; i < n; ++i) {
            const item = this.items[i];
            const reward = this.rewards[i];
            item.node.active = true;
            if (reward.raw.subType === 'unlimited_energy') {
                item.setStringTime(reward.quantity);
            } else {
                item.setQuantity(reward.quantity);
            }
            item.setIcon(reward.raw);
            reward.preview.load().then(view => {
                //item.setView(view);
            });
        }
    }
}
